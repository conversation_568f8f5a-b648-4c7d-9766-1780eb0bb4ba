from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.schemas import (
    SchemaBulkCreateParams,
    SchemaBulkDeleteParams,
    SchemaDetailResponse,
    SchemaListResponse,
    SchemaParams,
    SchemaResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def bulk_create_database_schemas(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    body: SchemaBulkCreateParams,
    ctx: Context,
) -> list[SchemaResponse]:
    """Bulk create database schemas."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/bulk_create",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


bulk_create_database_schemas_tool = Tool.from_function(
    fn=bulk_create_database_schemas,
    name="provably_bulk_create_database_schemas",
    description="Bulk create database schemas",
    enabled=True,
)


async def get_database_schema(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    ctx: Context,
) -> SchemaDetailResponse:
    """Get a schema."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}",
        api_key=api_key,
    )
    return response


get_database_schema_tool = Tool.from_function(
    fn=get_database_schema,
    name="provably_get_database_schema",
    description="Get database schema details",
    enabled=True,
)


async def update_database_schema(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    body: SchemaParams,
    ctx: Context,
) -> SchemaResponse:
    """Update a schema."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_database_schema_tool = Tool.from_function(
    fn=update_database_schema,
    name="provably_update_database_schema",
    description="Update database schema",
    enabled=True,
)


async def get_database_schemas(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    ctx: Context,
) -> list[SchemaListResponse]:
    """Get all database schemas."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas",
        api_key=api_key,
    )
    return response


get_database_schemas_tool = Tool.from_function(
    fn=get_database_schemas,
    name="provably_get_database_schemas",
    description="Get all database schemas",
    enabled=True,
)


async def bulk_delete_database_schemas(
    organization_id: UUID,
    body: SchemaBulkDeleteParams,
    ctx: Context,
) -> dict:
    """Bulk delete database schemas."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/schemas/bulk_delete",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return {"message": "Schemas deleted"}


bulk_delete_database_schemas_tool = Tool.from_function(
    fn=bulk_delete_database_schemas,
    name="provably_bulk_delete_database_schemas",
    description="Bulk delete database schemas",
    enabled=True,
)