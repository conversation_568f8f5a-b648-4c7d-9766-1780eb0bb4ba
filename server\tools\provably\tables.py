from uuid import <PERSON>UI<PERSON>

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.tables import (
    TableBulkDeleteParams,
    TableDetailResponse,
    TableListItem,
    TableParams,
    TableResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_database_tables(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    ctx: Context,
) -> list[TableListItem]:
    """Get all database tables."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables",
        api_key=api_key,
    )
    return response


get_database_tables_tool = Tool.from_function(
    fn=get_database_tables,
    name="provably_get_database_tables",
    description="Get all database tables",
    enabled=True,
)


async def get_database_table(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    ctx: Context,
) -> TableDetailResponse:
    """Get a database table."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}",
        api_key=api_key,
    )
    return response


get_database_table_tool = Tool.from_function(
    fn=get_database_table,
    name="provably_get_database_table",
    description="Get a database table",
    enabled=True,
)


async def update_database_table(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    schema_id: UUID,
    table_id: UUID,
    body: TableParams,
    ctx: Context,
) -> TableResponse:
    """Update a database table."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}/schemas/{schema_id}/tables/{table_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_database_table_tool = Tool.from_function(
    fn=update_database_table,
    name="provably_update_database_table",
    description="Update a database table",
    enabled=True,
)


async def bulk_delete_database_tables(
    organization_id: UUID,
    body: TableBulkDeleteParams,
    ctx: Context,
) -> dict:
    """Bulk delete database tables."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/tables/bulk_delete",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return {"message": "Tables deleted"}


bulk_delete_database_tables_tool = Tool.from_function(
    fn=bulk_delete_database_tables,
    name="provably_bulk_delete_database_tables",
    description="Bulk delete database tables",
    enabled=True,
)