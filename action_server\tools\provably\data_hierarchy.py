from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from action_server.contracts.provably.data_hierarchy import DataHierarchyResponse
from action_server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()

async def get_data_hierarchy(organization_id: UUID, ctx: Context) -> DataHierarchyResponse:
    """Get data hierarchy."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/data",
        api_key=api_key,
    )
    return response

get_data_hierarchy_tool = Tool.from_function(
    fn=get_data_hierarchy,
    name="provably_get_data_hierarchy",
    description="Get data hierarchy",
    enabled=True,
)
