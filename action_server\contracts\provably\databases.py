from uuid import UUID
from pydantic import BaseModel


class DatabaseListResponse(BaseModel):
    id: UUID
    name: str
    status: str

class DatabaseAccessParams(BaseModel):
    name: str
    username: str
    password: str
    provider: str
    uri: str

class DatabaseAccessResponse(BaseModel):
    is_admin: bool
    is_credentials_correct: bool

class DatabaseParams(BaseModel):
    name: str
    username: str
    password: str
    provider: str
    uri: str

class DatabaseResponse(BaseModel):
    id: str
    name: str

class MiddlewareInfo(BaseModel):
    id: UUID
    name: str
    status: str
    is_self_hosted: bool

class DatabaseDetailResponse(BaseModel):
    id: UUID
    name: str
    username: str
    provider: str
    uri: str
    is_admin: bool
    status: str
    middleware: MiddlewareInfo
    
