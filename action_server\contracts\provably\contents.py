from enum import Enum
from typing import Literal, Union
from uuid import UUID
from pydantic import BaseModel, Field

from .common import SortOrder

class ContentType(str, Enum):
    MIDDLEWARE = "middleware"
    DATABASE = "database"
    SCHEMA = "schema"
    TABLE = "table"
    COLUMN = "column"

class ContentsQueryParams(BaseModel):
    middleware_ids: str | None = None
    database_ids: str | None = None
    schema_ids: str | None = None
    table_ids: str | None = None
    column_ids: str | None = None
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)
    query: str | None = None
    sort_by: str | None = Field(None, description="Field to sort by (name, type, column_count, table_count, schema_count, database_count, collection_count)")
    sort_order: SortOrder | None = None
    
class ContentSearchItem(BaseModel):
    type: str
    id: UUID
    name: str
    column_type: str | None = None
    
class BaseContent(BaseModel):
    id: UUID
    name: str
    status: str

class DatabaseRef(BaseModel):
    id: UUID
    name: str
    
class MiddlewareRef(BaseModel):
    id: UUID
    name: str
    
class SchemaRef(BaseModel):
    id: UUID
    name: str


class TableRef(BaseModel):
    id: UUID
    name: str
    
class MiddlewareContent(BaseContent):
    is_self_hosted: bool
    database_count: int
    schema_count: int
    table_count: int
    column_count: int
    collection_count: int
    
class DatabaseContent(BaseContent):
    schema_count: int
    table_count: int
    column_count: int
    collection_count: int
    middleware: MiddlewareRef
    
class SchemaContent(BaseContent):
    is_imported: bool
    table_count: int
    column_count: int
    collection_count: int
    middleware: MiddlewareRef
    database: DatabaseRef


class TableContent(BaseContent):
    is_imported: bool
    column_count: int
    collection_count: int
    middleware: MiddlewareRef
    database: DatabaseRef
    schema: SchemaRef

class ColumnContent(BaseContent):
    is_imported: bool
    type: str
    collection_count: int
    middleware: MiddlewareRef
    database: DatabaseRef
    schema: SchemaRef
    table: TableRef
    
class MiddlewareResponse(BaseModel):
    content_type: Literal["middleware"]
    content: list[MiddlewareContent]


class DatabaseResponse(BaseModel):
    content_type: Literal["database"]
    content: list[DatabaseContent]


class SchemaResponse(BaseModel):
    content_type: Literal["schema"]
    content: list[SchemaContent]


class TableResponse(BaseModel):
    content_type: Literal["table"]
    content: list[TableContent]


class ColumnResponse(BaseModel):
    content_type: Literal["column"]
    content: list[ColumnContent]
    
OrganizationContentsResponse = Union[
    MiddlewareResponse,
    DatabaseResponse,
    SchemaResponse,
    TableResponse,
    ColumnResponse,
]