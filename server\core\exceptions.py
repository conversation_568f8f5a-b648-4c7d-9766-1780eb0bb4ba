class ResourceUnavailableError(Exception):
    """Custom exception for temporary resource failures"""

    pass


class ContentTooLargeError(Exception):
    """Custom exception for payload size limits"""

    pass


class RequestCancelledError(Exception):
    """Custom exception for cancelled operations"""

    pass


class ParseError(Exception):
    """Custom exception for JSON parsing errors"""

    pass


class InvalidRequestError(Exception):
    """Custom exception for malformed requests"""

    pass


class InvalidParamsError(Exception):
    """Custom exception for invalid parameters"""

    pass


class MethodNotFoundError(Exception):
    """Custom exception for unknown methods"""

    pass


class PMCPPermissionError(Exception):
    """Custom exception for PMCP-specific permission errors"""

    pass


class ProvablyAPIError(Exception):
    """Custom exception for Provably API-specific errors"""

    def __init__(
        self,
        method: str,
        endpoint: str,
        status_code: int | None = None,
        error_message: str = "",
    ):
        self.method = method
        self.endpoint = endpoint
        self.status_code = status_code
        self.error_message = error_message
        super().__init__(f"{method} {endpoint}: {error_message}")
