from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from action_server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def validate_database(
    organization_id: UUID,
    database_id: UUID,
    ctx: Context,
) -> dict:
    """Validate a database."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/validation/organizations/{organization_id}/databases/{database_id}",
        api_key=api_key,
    )

    return {"message": "Validation succeded"}


validate_database_tool = Tool.from_function(
    fn=validate_database,
    name="provably_validate_database",
    description="Validate a database",
    enabled=True,
)
