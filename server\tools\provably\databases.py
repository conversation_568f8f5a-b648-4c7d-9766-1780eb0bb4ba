from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from server.contracts.provably.databases import (
    DatabaseAccessParams,
    DatabaseAccessResponse,
    DatabaseDetailResponse,
    DatabaseListResponse,
    DatabaseParams,
    DatabaseResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def list_databases(
    organization_id: UUID, middleware_id: UUID, ctx: Context
) -> list[DatabaseListResponse]:
    """List all databases."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases",
        api_key=api_key,
    )
    return response


list_databases_tool = Tool.from_function(
    fn=list_databases,
    name="provably_list_databases",
    description="List all databases",
    enabled=True,
)


async def check_database_access(
    organization_id: UUID,
    middleware_id: UUID,
    body: DatabaseAccessParams,
    ctx: Context,
) -> DatabaseAccessResponse:
    """Check database access."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/access",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


check_database_access_tool = Tool.from_function(
    fn=check_database_access,
    name="provably_check_database_access",
    description="Check database access",
    enabled=True,
)


async def create_database(
    organization_id: UUID,
    middleware_id: UUID,
    body: DatabaseParams,
    ctx: Context,
) -> DatabaseResponse:
    """Create a new database."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


create_database_tool = Tool.from_function(
    fn=create_database,
    name="provably_create_database",
    description="Create a new database",
    enabled=True,
)


async def get_database(
    organization_id: UUID, middleware_id: UUID, database_id: UUID, ctx: Context
) -> DatabaseDetailResponse:
    """Get a database."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}",
        api_key=api_key,
    )
    return response


get_database_tool = Tool.from_function(
    fn=get_database,
    name="provably_get_database",
    description="Get a database",
    enabled=True,
)


async def update_database(
    organization_id: UUID,
    middleware_id: UUID,
    database_id: UUID,
    body: DatabaseParams,
    ctx: Context,
) -> DatabaseResponse:
    """Update a database."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}/databases/{database_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response

update_database_tool = Tool.from_function(
    fn=update_database,
    name="provably_update_database",
    description="Update a database",
    enabled=True,
)


async def bulk_delete_databases(
    organization_id: UUID, body: list[UUID], ctx: Context
) -> dict:
    """Bulk delete databases."""
    api_key = ctx.get_state("api_key")
    await client.post(
        endpoint=f"/organizations/{organization_id}/databases/bulk_delete",
        api_key=api_key,
        data=body,
    )
    return {"message": "Databases deleted"}


bulk_delete_databases_tool = Tool.from_function(
    fn=bulk_delete_databases,
    name="provably_bulk_delete_databases",
    description="Bulk delete databases",
    enabled=True,
)