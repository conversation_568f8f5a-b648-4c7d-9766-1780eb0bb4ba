from uuid import UUI<PERSON>

from fastmcp import Context
from fastmcp.tools import Tool


from server.contracts.provably.organization_settings import (
    OrganizationNotificationQueryParams,
    OrganizationNotificationResponse,
    OrganizationNotificationSettingsResponse,
    UpdateOrganizationNotificationSettings,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_organization_notifications_settings(
    organzation_id: UUID, ctx: Context
) -> list[OrganizationNotificationSettingsResponse]:
    """Get organization notifications settings."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organzation_id}/notifications/settings",
        api_key=api_key,
    )
    return response


get_organization_notifications_settings_tool = Tool.from_function(
    fn=get_organization_notifications_settings,
    name="provably_get_organization_notifications_settings",
    description="Get organization notifications settings",
    enabled=True,
)


async def update_organization_notifications_settings(
    organization_id: UUID, body: UpdateOrganizationNotificationSettings, ctx: Context
) -> OrganizationNotificationSettingsResponse:
    """Update organization notifications settings."""
    api_key = ctx.get_state("api_key")
    response = await client.put(
        endpoint=f"/organizations/{organization_id}/notifications/settings",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_organization_notifications_settings_tool = Tool.from_function(
    fn=update_organization_notifications_settings,
    name="provably_update_organization_notifications_settings",
    description="Update organization notifications settings",
    enabled=True,
)


async def get_organization_notifications_count(
    organization_id: UUID, ctx: Context
) -> int:
    """Get organization notifications count."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/notifications/counts",
        api_key=api_key,
    )
    return response


get_organization_notifications_count_tool = Tool.from_function(
    fn=get_organization_notifications_count,
    name="provably_get_organization_notifications_count",
    description="Get organization notifications count",
    enabled=True,
)

async def get_organization_notifications(
    organization_id: UUID, params: OrganizationNotificationQueryParams, ctx: Context
) -> OrganizationNotificationResponse:
    """Get organization notifications."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/notifications",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return response


get_organization_notifications_tool = Tool.from_function(
    fn=get_organization_notifications,
    name="provably_get_organization_notifications",
    description="Get organization notifications",
    enabled=True,
)