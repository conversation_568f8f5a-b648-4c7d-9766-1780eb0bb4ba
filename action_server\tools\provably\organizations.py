from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from action_server.contracts.provably.organizations import (
    ChangeRoleParams,
    GetUsersParams,
    InviteUserListParams,
    OrganizationParams,
    OrganizationResponse,
    OrganizationUser,
)
from action_server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def list_organizations(ctx: Context) -> list[OrganizationResponse]:
    """List collections with optional filtering and pagination."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint="/organizations",
        api_key=api_key,
    )
    return [OrganizationResponse(**item) for item in response]


list_organizations_tool = Tool.from_function(
    fn=list_organizations,
    name="provably_list_organizations",
    description="List Provably organizations",
    enabled=True,
)


async def add_organization(
    body: OrganizationParams, ctx: Context
) -> OrganizationResponse:
    """Add a new organization."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint="/organizations",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return OrganizationResponse(**response)


add_organization_tool = Tool.from_function(
    fn=add_organization,
    name="provably_add_organization",
    description="Add a new organization",
    enabled=True,
)


async def get_organization(organization_id: UUID, ctx: Context) -> OrganizationResponse:
    """Get an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}",
        api_key=api_key,
    )
    return OrganizationResponse(**response)


get_organization_tool = Tool.from_function(
    fn=get_organization,
    name="provably_get_organization",
    description="Get an organization",
    enabled=True,
)


async def delete_organization(organization_id: UUID, ctx: Context) -> dict:
    """Delete an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.delete(
        endpoint=f"/organizations/{organization_id}",
        api_key=api_key,
    )
    return response


delete_organization_tool = Tool.from_function(
    fn=delete_organization,
    name="provably_delete_organization",
    description="Delete an organization",
    enabled=True,
)


async def update_organization(
    organization_id: str, body: OrganizationParams, ctx: Context
) -> OrganizationResponse:
    """Update an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.patch(
        endpoint=f"/organizations/{organization_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return OrganizationResponse(**response)


update_organization_tool = Tool.from_function(
    fn=update_organization,
    name="provably_update_organization",
    description="Update an organization",
    enabled=True,
)


async def leave_organization(organization_id: UUID, ctx: Context) -> dict:
    """Leave an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.delete(
        endpoint=f"/organizations/{organization_id}/access",
        api_key=api_key,
    )
    return response


leave_organization_tool = Tool.from_function(
    fn=leave_organization,
    name="provably_leave_organization",
    description="Leave an organization",
    enabled=True,
)


async def get_users_in_organization(
    organization_id: UUID, params: GetUsersParams, ctx: Context
) -> list[OrganizationUser]:
    """Get users in an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/users",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return [OrganizationUser(**item) for item in response]


get_users_in_organization_tool = Tool.from_function(
    fn=get_users_in_organization,
    name="provably_get_users_in_organization",
    description="Get users in an organization",
    enabled=True,
)


async def update_organization_user(
    organization_id: UUID, user_id: UUID, body: ChangeRoleParams, ctx: Context
) -> OrganizationUser:
    """Update an organization user."""
    api_key = ctx.get_state("api_key")
    response = await client.patch(
        endpoint=f"/organizations/{organization_id}/users/{user_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return OrganizationUser(**response)


update_organization_user_tool = Tool.from_function(
    fn=update_organization_user,
    name="provably_update_organization_user",
    description="Update an organization user",
    enabled=True,
)


async def bulk_invite_users_to_organization(
    organization_id: UUID, body: InviteUserListParams, ctx: Context
) -> dict:
    """Bulk invite users to an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/users/bulk_invite",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


bulk_invite_users_to_organization_tool = Tool.from_function(
    fn=bulk_invite_users_to_organization,
    name="provably_bulk_invite_users_to_organization",
    description="Bulk invite users to an organization",
    enabled=True,
)


async def bulk_delete_users_from_organization(
    organization_id: UUID, body: list[UUID], ctx: Context
) -> dict:
    """Bulk delete users from an organization."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/users/bulk_delete",
        api_key=api_key,
        data=body,
    )
    return response


bulk_delete_users_from_organization_tool = Tool.from_function(
    fn=bulk_delete_users_from_organization,
    name="provably_bulk_delete_users_from_organization",
    description="Bulk delete users from an organization",
    enabled=True,
)
