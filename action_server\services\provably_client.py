from typing import Any

import httpx
from loguru import logger

from action_server.core.config import Config
from shared.exceptions import (
    InvalidParamsError,
    PMCPPermissionError,
    ProvablyAPIError,
    ResourceUnavailableError,
)


class ProvablyHTTPClient:
    """HTTP client for Provably API with authentication and error handling."""

    def __init__(self):
        self.base_url = Config.PROVABLY_BASE_URL.rstrip("/")

    def _get_auth_headers(
        self, api_key: str, additional_headers: dict | None = None
    ) -> dict:
        """Get headers with authentication."""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}",
        }
        if additional_headers:
            headers.update(additional_headers)
        return headers

    def _handle_http_error(self, method: str, endpoint: str, error: Exception) -> None:
        """Map HTTP errors to PMCP exceptions."""
        if isinstance(error, httpx.HTTPStatusError):
            status_code = error.response.status_code

            if status_code == 401:
                raise PMCPPermissionError("Authentication failed")
            elif status_code == 403:
                raise PMCPPermissionError("Access forbidden")
            elif status_code == 404:
                raise ResourceUnavailableError("Resource not found")
            elif status_code in (400, 422):
                raise InvalidParamsError(
                    f"Invalid request parameters: {error.response.text}"
                )
            elif status_code >= 500:
                raise ResourceUnavailableError("Provably API server error")
            else:
                raise ProvablyAPIError(method, endpoint, status_code, str(error))

        elif isinstance(error, (httpx.TimeoutException, httpx.ConnectError)):
            raise ResourceUnavailableError(f"Network error: {str(error)}")

        elif isinstance(error, httpx.RequestError):
            raise ResourceUnavailableError(f"Request error: {str(error)}")

        else:
            raise ProvablyAPIError(method, endpoint, None, str(error))

    def _parse_response(self, response: httpx.Response) -> Any:
        """Parse response based on content type and content."""
        # Handle empty responses
        if not response.content:
            return None

        # Get content type header
        content_type = response.headers.get("content-type", "").lower()

        # Handle JSON responses
        if "application/json" in content_type or "text/json" in content_type:
            try:
                return response.json()
            except Exception:
                # If JSON parsing fails, fall back to text
                return response.text

        # Handle text responses
        if any(text_type in content_type for text_type in ["text/", "application/xml", "application/yaml"]):
            return response.text

        # Handle binary responses (images, PDFs, etc.)
        if any(binary_type in content_type for binary_type in [
            "application/octet-stream", "application/pdf", "image/", "video/", "audio/"
        ]):
            return response.content

        # Default: try JSON first, then text, then bytes
        try:
            return response.json()
        except Exception:
            try:
                return response.text
            except Exception:
                return response.content

    async def get(
        self,
        endpoint: str,
        api_key: str,
        params: dict | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async GET request to the Provably API."""

        logger.debug(f"Making GET request to {endpoint} endpoint", params=params)

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    params=params,
                    headers=self._get_auth_headers(api_key, headers),
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(
                f"Error making GET request to {endpoint} with params {params}: {e}"
            )
            self._handle_http_error("GET", endpoint, e)

    async def post(
        self,
        endpoint: str,
        api_key: str,
        data: dict | list | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async POST request to the Provably API."""

        logger.debug(f"Making POST request to {endpoint} endpoint")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    json=data,
                    headers=self._get_auth_headers(api_key, headers),
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(
                f"Error making POST request to {endpoint} with data {data}: {e}"
            )
            self._handle_http_error("POST", endpoint, e)

    async def post_multipart(
        self,
        endpoint: str,
        api_key: str,
        files: dict[str, Any] | None = None,
        data: dict[str, str] | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async POST request with multipart form data to the Provably API.

        Args:
            endpoint: API endpoint
            api_key: Authentication API key
            files: Dict of file fields in format {field_name: (filename, content, content_type)}
            data: Dict of form data fields
            headers: Additional headers
            timeout: Request timeout
        """

        logger.debug(f"Making multipart POST request to {endpoint} endpoint")

        try:
            # Prepare auth headers without Content-Type (httpx will set it for multipart)
            auth_headers = {
                "Authorization": f"Bearer {api_key}",
            }
            if headers:
                auth_headers.update(headers)

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    files=files,
                    data=data,
                    headers=auth_headers,
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(
                f"Error making multipart POST request to {endpoint}: {e}"
            )
            self._handle_http_error("POST", endpoint, e)

    async def patch(
        self,
        endpoint: str,
        api_key: str,
        data: dict | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async PATCH request to the Provably API."""

        logger.debug(f"Making PATCH request to {endpoint} endpoint")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    json=data,
                    headers=self._get_auth_headers(api_key, headers),
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(
                f"Error making PATCH request to {endpoint} with data {data}: {e}"
            )
            self._handle_http_error("PATCH", endpoint, e)

    async def put(
        self,
        endpoint: str,
        api_key: str,
        data: dict | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async PUT request to the Provably API."""

        logger.debug(f"Making PUT request to {endpoint} endpoint")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    json=data,
                    headers=self._get_auth_headers(api_key, headers),
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(
                f"Error making PUT request to {endpoint} with data {data}: {e}"
            )
            self._handle_http_error("PUT", endpoint, e)

    async def delete(
        self,
        endpoint: str,
        api_key: str,
        params: dict | None = None,
        headers: dict | None = None,
        timeout: float = 30.0,
    ) -> Any:
        """Make an async DELETE request to the Provably API."""

        logger.debug(f"Making DELETE request to {endpoint} endpoint")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.base_url}/{endpoint.lstrip('/')}",
                    params=params,
                    headers=self._get_auth_headers(api_key, headers),
                    timeout=timeout,
                )
                response.raise_for_status()
                return self._parse_response(response)

        except Exception as e:
            logger.error(f"Error making DELETE request to {endpoint}: {e}")
            self._handle_http_error("DELETE", endpoint, e)


class OpenProvablyHTTPClientV1(ProvablyHTTPClient):
    """HTTP client for Provably API v1."""

    def __init__(self):
        super().__init__()
        self.base_url = f"{self.base_url}/api/v1/open"
