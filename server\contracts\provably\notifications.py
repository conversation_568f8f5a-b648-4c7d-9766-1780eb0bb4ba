from datetime import datetime
from enum import Enum
from uuid import UUID
from pydantic import BaseModel, Field
from typing import Literal, Union


class NotificationQueryParams(BaseModel):
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)


class UserBasic(BaseModel):
    id: UUID
    username: str | None = None
    email: str | None = None


class OrganizationBasic(BaseModel):
    id: UUID
    name: str
    description: str | None = None


class CollectionBasic(BaseModel):
    id: UUID
    name: str
    description: str | None = None


class ProofBasic(BaseModel):
    id: UUID


class NotificationBaseResponse(BaseModel):
    """Base notification response that all types will extend"""

    id: UUID
    created_at: datetime
    is_read: bool


class OrganizationInviteReceivedResponse(NotificationBaseResponse):
    type: Literal["organization_invite_received"]
    organization: OrganizationBasic
    invited_by: UserBasic
    status: str


class CollectionInviteReceivedResponse(NotificationBaseResponse):
    type: Literal["collection_invite_received"]
    organization: OrganizationBasic
    collection: CollectionBasic
    invited_by: UserBasic
    status: str


class CollectionAccessGrantedResponse(NotificationBaseResponse):
    type: Literal["collection_access_granted"]
    organization: OrganizationBasic
    collection: CollectionBasic


class OrganizationRoleUpdatedResponse(NotificationBaseResponse):
    type: Literal["organization_role_updated"]
    organization: OrganizationBasic
    role: str
    updated_by: UserBasic


class CollectionRepairCompletedResponse(NotificationBaseResponse):
    type: Literal["collection_repair_completed"]
    organization: OrganizationBasic
    collection: CollectionBasic


class ProofGenerationCompletedResponse(NotificationBaseResponse):
    type: Literal["proof_generation_completed"]
    proof: ProofBasic


# Discriminated union type for notifications - matches frontend Zod schema
NotificationResponse = list[
    Union[
        OrganizationInviteReceivedResponse,
        CollectionInviteReceivedResponse,
        CollectionAccessGrantedResponse,
        OrganizationRoleUpdatedResponse,
        CollectionRepairCompletedResponse,
        ProofGenerationCompletedResponse,
    ]
]


class NotificationCountResponse(BaseModel):
    recent_count: int
    unread_count: int


class NotificationSettingCategory(BaseModel):
    in_app: bool
    email: bool


class NotificationSettingsResponse(BaseModel):
    organization_activity: NotificationSettingCategory
    collection_activity: NotificationSettingCategory
    bell_alerts: NotificationSettingCategory


class UpdateNotificationSettingCategory(BaseModel):
    in_app: bool | None = None
    email: bool | None = None


class UpdateNotificationSettings(BaseModel):
    organization_activity: UpdateNotificationSettingCategory | None = None
    collection_activity: UpdateNotificationSettingCategory | None = None
    bell_alerts: UpdateNotificationSettingCategory | None = None
