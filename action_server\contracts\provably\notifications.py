from pydantic import BaseModel, Field

class NotificationQueryParams(BaseModel):
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct UserBasic {
    pub id: Uuid,
    pub username: Option<String>,
    pub email: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct OrganizationBasic {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct CollectionBasic {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct ProofBasic {
    pub id: Uuid,
}

// Base notification response that all types will extend
#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct NotificationBaseResponse {
    pub id: Uuid,
    pub created_at: DateTime<Utc>,
    pub is_read: bool,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct OrganizationInviteReceivedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub organization: OrganizationBasic,
    pub invited_by: UserBasic,
    pub status: String,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct CollectionInviteReceivedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub organization: OrganizationBasic,
    pub collection: CollectionBasic,
    pub invited_by: UserBasic,
    pub status: String,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct CollectionAccessGrantedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub organization: OrganizationBasic,
    pub collection: CollectionBasic,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct OrganizationRoleUpdatedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub organization: OrganizationBasic,
    pub role: String,
    pub updated_by: UserBasic,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct CollectionRepairCompletedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub organization: OrganizationBasic,
    pub collection: CollectionBasic,
}

#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct ProofGenerationCompletedResponse {
    #[serde(flatten)]
    pub base: NotificationBaseResponse,
    pub proof: ProofBasic,
}

pub enum NotificationResponse {
    #[serde(rename = "organization_invite_received")]
    OrganizationInviteReceived(OrganizationInviteReceivedResponse),
    #[serde(rename = "collection_invite_received")]
    CollectionInviteReceived(CollectionInviteReceivedResponse),
    #[serde(rename = "collection_access_granted")]
    CollectionAccessGranted(CollectionAccessGrantedResponse),
    #[serde(rename = "organization_role_updated")]
    OrganizationRoleUpdated(OrganizationRoleUpdatedResponse),
    #[serde(rename = "collection_repair_completed")]
    CollectionRepairCompleted(CollectionRepairCompletedResponse),
    #[serde(rename = "proof_generation_completed")]
    ProofGenerationCompleted(ProofGenerationCompletedResponse),
}