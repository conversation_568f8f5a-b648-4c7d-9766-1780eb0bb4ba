from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field
from typing import Literal


class NotificationQueryParams(BaseModel):
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)


class UserBasic(BaseModel):
    id: UUID
    username: str | None = None
    email: str | None = None


class OrganizationBasic(BaseModel):
    id: UUID
    name: str
    description: str | None = None


class CollectionBasic(BaseModel):
    id: UUID
    name: str
    description: str | None = None


class ProofBasic(BaseModel):
    id: UUID


class NotificationBaseResponse(BaseModel):
    """Base notification response that all types will extend"""
    id: UUID
    created_at: datetime
    is_read: bool


class OrganizationInviteReceivedResponse(NotificationBaseResponse):
    organization: OrganizationBasic
    invited_by: UserBasic
    status: str


class CollectionInviteReceivedResponse(NotificationBaseResponse):
    organization: OrganizationBasic
    collection: CollectionBasic
    invited_by: UserBasic
    status: str


class CollectionAccessGrantedResponse(NotificationBaseResponse):
    organization: OrganizationBasic
    collection: CollectionBasic


class OrganizationRoleUpdatedResponse(NotificationBaseResponse):
    organization: OrganizationBasic
    role: str
    updated_by: UserBasic


class CollectionRepairCompletedResponse(NotificationBaseResponse):
    organization: OrganizationBasic
    collection: CollectionBasic


class ProofGenerationCompletedResponse(NotificationBaseResponse):
    proof: ProofBasic


class NotificationResponse(BaseModel):
    """Union type for different notification responses"""
    notification_type: Literal[
        "organization_invite_received",
        "collection_invite_received",
        "collection_access_granted",
        "organization_role_updated",
        "collection_repair_completed",
        "proof_generation_completed"
    ]
    data: (
        OrganizationInviteReceivedResponse |
        CollectionInviteReceivedResponse |
        CollectionAccessGrantedResponse |
        OrganizationRoleUpdatedResponse |
        CollectionRepairCompletedResponse |
        ProofGenerationCompletedResponse
    )