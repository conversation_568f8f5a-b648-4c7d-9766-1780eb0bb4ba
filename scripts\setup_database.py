#!/usr/bin/env python3
"""
Database setup script for PMCP.

This script handles database initialization for different environments:
- Development: Creates database and runs migrations
- Production: Runs migrations only (assumes database exists)
"""

import asyncio
import shutil
import subprocess
import sys
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

import click  # noqa: E402
from loguru import logger  # noqa: E402

from server.core.config import Config  # noqa: E402
from database.init_db import _initialize_database_if_not_exist  # noqa: E402


def _find_uv_executable() -> str:
    """Find the UV executable, handling Windows .bat files and different installation methods."""
    # Try to find uv using shutil.which
    uv_path = shutil.which("uv")
    if uv_path:
        return uv_path

    # Fallback: try common locations
    possible_paths = [
        "uv",  # In case it's in PATH but shutil.which didn't find it
        "uv.exe",
        "uv.bat",
    ]

    for path in possible_paths:
        if shutil.which(path):
            return path

    # If nothing found, return "uv" and let subprocess handle the error
    return "uv"


@click.command()
@click.option(
    "--environment",
    "-e",
    type=click.Choice(["local", "dev", "prod"]),
    default=None,
    help="Environment to set up (defaults to APP_ENV)",
)
@click.option(
    "--create-db/--no-create-db",
    default=None,
    help="Whether to create database if it doesn't exist",
)
@click.option(
    "--run-migrations/--no-migrations", default=True, help="Whether to run migrations"
)
@click.option(
    "--test-connection/--no-test",
    default=True,
    help="Whether to test database connection after setup",
)
def setup_database(environment, create_db, run_migrations, test_connection):
    """Set up the database for PMCP."""

    # Determine environment
    env = environment or Config.ENVIRONMENT
    logger.info(f"Setting up database for environment: {env}")

    # Determine if we should create database
    if create_db is None:
        create_db = env in ("local", "dev")

    logger.info("Configuration:")
    logger.info(f"  Environment: {env}")
    logger.info(f"  Create database: {create_db}")
    logger.info(f"  Run migrations: {run_migrations}")
    logger.info(f"  Test connection: {test_connection}")
    logger.info(f"  Database URL: {Config.DB_URL}")

    asyncio.run(_setup_database_async(create_db, run_migrations, test_connection))


async def _setup_database_async(
    create_db: bool, run_migrations: bool, test_connection: bool
):
    """Async database setup logic."""

    # Step 1: Create database if needed
    if create_db:
        logger.info("Step 1: Creating database if it doesn't exist...")
        try:
            await _initialize_database_if_not_exist()
            logger.success("✓ Database creation step completed")
        except Exception as e:
            logger.error(f"✗ Failed to create database: {e}")
            sys.exit(1)
    else:
        logger.info("Step 1: Skipping database creation (production mode)")

    # Step 2: Run migrations
    if run_migrations:
        logger.info("Step 2: Running database migrations...")
        try:
            uv_executable = _find_uv_executable()
            result = subprocess.run(
                [uv_executable, "run", "alembic", "upgrade", "head"],
                cwd=PROJECT_ROOT,
                capture_output=True,
                text=True,
                shell=True,  # Required for Windows .bat files
            )

            if result.returncode == 0:
                logger.success("✓ Migrations completed successfully")
                if result.stdout.strip():
                    logger.info(f"Migration output:\n{result.stdout}")
            else:
                logger.error(f"✗ Migration failed: {result.stderr}")
                sys.exit(1)

        except Exception as e:
            logger.exception(f"✗ Error running migrations: {e}")
            sys.exit(1)
    else:
        logger.info("Step 2: Skipping migrations")

    # Step 3: Test connection
    if test_connection:
        logger.info("Step 3: Testing database connection...")
        try:
            # Import here to avoid circular imports
            from sqlmodel import select

            from database.session import db_async_session_maker

            async with db_async_session_maker() as session:
                result = await session.execute(select(1))
                value = result.scalar()

                if value == 1:
                    logger.success("✓ Database connection test passed")
                else:
                    logger.error(
                        "✗ Database connection test failed - unexpected result"
                    )
                    sys.exit(1)

        except Exception as e:
            logger.error(f"✗ Database connection test failed: {e}")
            sys.exit(1)
    else:
        logger.info("Step 3: Skipping connection test")

    logger.success("🎉 Database setup completed successfully!")


@click.group()
def cli():
    """Database management commands for PMCP."""
    pass


cli.add_command(setup_database, name="setup")


if __name__ == "__main__":
    cli()
