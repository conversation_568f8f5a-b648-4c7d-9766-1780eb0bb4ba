from datetime import datetime
from enum import Enum
from uuid import UUID
from pydantic import BaseModel

class ProofStatus(str, Enum):
    PENDING = "Pending"
    COMPLETED = "Completed"
    FAILED = "Failed"

class VerificationStatus(str, Enum):
    UNVERIFIED = "Unverified"
    VERIFIED = "Verified"
    FAILED = "Failed"
    VERIFYING = "Verifying"

class UserInfo(BaseModel):
    username: str
    email: str

class CollectionInfo(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    image: str | None = None

class ProofInfo(BaseModel):
    id: UUID
    status: ProofStatus
    generated_at: datetime | None = None
    verification_status: VerificationStatus
    last_verified_at: datetime | None = None

class QueryRecordResponse(BaseModel):
    id: UUID
    created_at: datetime
    query_explanation: str | None = None
    user: UserInfo
    collection: CollectionInfo
    nat_lang_query: str
    sql_query: str
    result: dict | None = None
    proof: ProofInfo | None = None

