from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field

class CollectionAccessRequestQueryParams(BaseModel):
    collection_ids: str | None = Field(None, description="Filter by specific collection IDs (comma-separated list of UUIDs)")
    user_ids: str | None = Field(None, description="Filter by specific user IDs (comma-separated list of UUIDs)")
    date_from: str | None = Field(None, description="Filter by date from (ISO format)")
    date_to: str | None = Field(None, description="Filter by date to (ISO format)")
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)
    sort_by: str | None = Field(None, description="Sort by field (created_at, user, collection)")
    sort_order: str | None = Field(None, description="Sort order (asc or desc, default is desc)")
    
class CollectionAccessRequestUser(BaseModel):
    username: str
    email: str
    
class CollectionAccessRequestCollection(BaseModel):
    id: UUID
    name: str
    
class CollectionAccessRequestResponse(BaseModel):
    id: UUID
    created_at: datetime
    user: CollectionAccessRequestUser
    collection: CollectionAccessRequestCollection