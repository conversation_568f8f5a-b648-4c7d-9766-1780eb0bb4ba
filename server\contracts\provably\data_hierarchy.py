from uuid import UUID

from pydantic import BaseModel


class TableInfo(BaseModel):
    id: UUID
    name: str
    status: str
    is_imported: bool
    column_count: int
    has_invalid_columns: bool


class SchemaInfo(BaseModel):
    id: UUID
    name: str
    status: str
    is_imported: bool
    tables: list[TableInfo]


class DatabaseInfo(BaseModel):
    id: UUID
    name: str
    status: str
    schemas: list[SchemaInfo]


class MiddlewareInfo(BaseModel):
    id: UUID
    name: str
    status: str
    is_self_hosted: bool
    databases: list[DatabaseInfo]


class DataHierarchyResponse(BaseModel):
    middlewares: list[MiddlewareInfo]
