from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class OrganizationLogQueryParams(BaseModel):
    user_ids: str | None = Field(
        None, description="Filter by specific user IDs (comma-separated list)"
    )
    actions: str | None = Field(
        None, description="Filter by specific actions (comma-separated list)"
    )
    middleware_ids: str | None = Field(
        None, description="Filter by middleware IDs (comma-separated list)"
    )
    database_ids: str | None = Field(
        None, description="Filter by database IDs (comma-separated list)"
    )
    schema_ids: str | None = Field(
        None, description="Filter by schema IDs (comma-separated list)"
    )
    table_ids: str | None = Field(
        None, description="Filter by table IDs (comma-separated list)"
    )
    column_ids: str | None = Field(
        None, description="Filter by column IDs (comma-separated list)"
    )
    collection_ids: str | None = Field(
        None, description="Filter by collection IDs (comma-separated list)"
    )
    date_from: str | None = Field(None, description="Filter by date from (ISO format)")
    date_to: str | None = Field(None, description="Filter by date to (ISO format)")
    sort_by: str | None = Field(
        None, description="Sort by field (created_at, action, entity_name)"
    )
    sort_order: str | None = Field(None, description="Sort order (asc or desc)")
    page: int | None = Field(None, description="Page number (0-based)")
    page_size: int | None = Field(None, description="Page size")


class LogUserResponse(BaseModel):
    username: str
    email: str


class LogEntityResponse(BaseModel):
    name: str


class OrganizationLogResponse(BaseModel):
    id: UUID = Field(..., description="Log entry ID")
    created_at: datetime = Field(..., description="Timestamp when the log was created")
    user: LogUserResponse | None = None
    action: str = Field(
        ...,
        description="Action performed (e.g., 'middleware_created', 'database_updated')",
    )
    entity: LogEntityResponse = Field(..., description="Entity information")
    details: str | None = Field(None, description="Additional details about the action")
