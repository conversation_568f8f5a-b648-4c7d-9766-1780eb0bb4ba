from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool


from server.contracts.provably.organization_logs import (
    OrganizationLogQueryParams,
    OrganizationLogResponse,
)
from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_organization_logs(
    organization_id: UUID, params: OrganizationLogQueryParams, ctx: Context
) -> list[OrganizationLogResponse]:
    """Get organization logs."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/logs",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return response


get_organization_logs_tool = Tool.from_function(
    fn=get_organization_logs,
    name="provably_get_organization_logs",
    description="Get organization logs",
    enabled=True,
)
