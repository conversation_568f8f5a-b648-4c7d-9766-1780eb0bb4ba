from fastmcp import Context
from fastmcp.tools import Tool

from server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def create_feadback(description: str, ctx: Context) -> dict:
    """Create a new feedback."""
    api_key = ctx.get_state("api_key")
    await client.post_multipart(
        endpoint="/feedback",
        api_key=api_key,
        data={"description": description},
        files={
            "description": (None, description)  # None filename -> text field
        },
    )
    return {"message": "Feedback created"}


create_feedback_tool = Tool.from_function(
    fn=create_feadback,
    name="provably_create_feedback",
    description="Create a new feedback for Provably",
    enabled=True,
)
