from fastmcp import Context
from fastmcp.tools import Tool

from action_server.contracts.provably.notifications import NotificationQueryParams
from action_server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def get_user_notifications(params: NotificationQueryParams, ctx: Context) -> dict:
    """Get user notifications."""
    api_key = ctx.get_state("api_key")
    response = await client.get(endpoint="/user/notifications", api_key=api_key)
    return response

get_user_notifications_tool = Tool.from_function(
    fn=get_user_notifications,
    name="provably_get_user_notifications",
    description="Get user notifications",
    enabled=True,
)
