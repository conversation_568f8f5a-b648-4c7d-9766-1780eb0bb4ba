from uuid import <PERSON>UID

from fastmcp import Context
from fastmcp.tools import Tool


from action_server.contracts.provably.middlewares import (
    MiddlewareResponse,
    UpdateMiddlewareParams,
)
from action_server.services.provably_client import OpenProvablyHTTPClientV1

client = OpenProvablyHTTPClientV1()


async def list_middlewares(
    organization_id: UUID, ctx: Context
) -> list[MiddlewareResponse]:
    """List all middlewares."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares",
        api_key=api_key,
    )
    return response


list_middlewares_tool = Tool.from_function(
    fn=list_middlewares,
    name="provably_list_middlewares",
    description="List all middlewares",
    enabled=True,
)


async def add_provably_middleware(
    organization_id: UUID, ctx: Context
) -> MiddlewareResponse:
    """Add a new middleware."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/provably",
        api_key=api_key,
    )
    return response


add_provably_middleware_tool = Tool.from_function(
    fn=add_provably_middleware,
    name="provably_add_provably_middleware",
    description="Add a new middleware",
    enabled=True,
)


async def get_middleware(
    organization_id: UUID, middleware_id: UUID, ctx: Context
) -> MiddlewareResponse:
    """Get a middleware."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}",
        api_key=api_key,
    )
    return response


get_middleware_tool = Tool.from_function(
    fn=get_middleware,
    name="provably_get_middleware",
    description="Get a middleware",
    enabled=True,
)


async def update_middleware(
    organization_id: UUID,
    middleware_id: UUID,
    body: UpdateMiddlewareParams,
    ctx: Context,
) -> MiddlewareResponse:
    """Update a middleware."""
    api_key = ctx.get_state("api_key")
    response = await client.patch(
        endpoint=f"/organizations/{organization_id}/middlewares/{middleware_id}",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


update_middleware_tool = Tool.from_function(
    fn=update_middleware,
    name="provably_update_middleware",
    description="Update a middleware",
    enabled=True,
)


async def bulk_delete_middlewares(
    organization_id: UUID, body: list[UUID], ctx: Context
) -> dict:
    """Bulk delete middlewares."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/middlewares/bulk_delete",
        api_key=api_key,
        data=body,
    )
    return response


bulk_delete_middlewares_tool = Tool.from_function(
    fn=bulk_delete_middlewares,
    name="provably_bulk_delete_middlewares",
    description="Bulk delete middlewares",
    enabled=True,
)
