#!/usr/bin/env python3
"""
Test script for the verify_external_proof functionality.
This script demonstrates how to use the updated verify_external_proof tool
with multipart form data.
"""

import asyncio
from uuid import UUID, uuid4

from fastmcp import Context

from action_server.tools.provably.query_records import verify_external_proof


async def test_verify_external_proof():
    """Test the verify_external_proof function with sample data."""
    
    # Sample proof data (comma-separated numbers as mentioned in the requirement)
    proof_data = "99,105,116,121,0,0,0,0,0,48,0,0,0,0"
    
    # Sample organization ID (you would use a real one in practice)
    organization_id = uuid4()
    
    # Create a mock context with API key
    ctx = Context()
    ctx.set_state("api_key", "your-api-key-here")  # Replace with actual API key
    
    try:
        print(f"Testing verify_external_proof with:")
        print(f"  Organization ID: {organization_id}")
        print(f"  Proof data: {proof_data}")
        print(f"  Filename: proof.txt")
        print()
        
        # Call the function
        result = await verify_external_proof(
            organization_id=organization_id,
            proof_data=proof_data,
            ctx=ctx,
            filename="test_proof.txt"
        )
        
        print("✅ Success! Response:")
        print(f"  Verified: {result.verified}")
        print(f"  Message: {result.message}")
        print(f"  Proof ID: {result.proof_id}")
        print(f"  Details: {result.verification_details}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")


def main():
    """Main function to run the test."""
    print("🧪 Testing verify_external_proof with multipart form data")
    print("=" * 60)
    
    # Run the async test
    asyncio.run(test_verify_external_proof())


if __name__ == "__main__":
    main()
