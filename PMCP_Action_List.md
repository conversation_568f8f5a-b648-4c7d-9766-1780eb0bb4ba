# PMCP Action List

This document provides a comprehensive list of all available actions in the Provably Model Context Protocol (PMCP) Server. The PMCP server acts as a trusted gateway for AI agents to interact with the Provably API, providing secure, auditable access to Provably services.

## Overview

The PMCP server provides **67 tools** across **12 categories** for managing Provably resources. All tools require API key authentication and provide structured request/response handling with Pydantic validation.

## Authentication

All tools require a valid API key passed via the `api_key` parameter in the context. Invalid API keys result in `PMCPPermissionError`.

---

## Collections

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_list_collections` | List collections with optional filtering and pagination | `organization_id` (UUID) | `collection_ids`, `column_ids`, `database_ids`, `middleware_ids`, `schema_ids`, `table_ids`, `collection_column_ids`, `page`, `page_size`, `sort_order`, `sort_by`, `query`, `is_valid`, `publicity_status` | `id`, `image`, `name`, `description`, `query_count`, `column_count`, `row_count`, `is_valid`, `publicity_status`, `is_creator`, `is_creator_admin`, `can_query` | Pagination limited by API constraints |
| `provably_create_collection` | Create a new collection | `organization_id` (UUID), `name` (str), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID), `table_id` (UUID), `enabled_columns` (list[EnabledColumn]), `is_descriptions_generated` (bool) | `description` (str) | Collection creation response | Requires valid middleware, database, schema, and table references |

---

## Databases

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_list_databases` | List all databases | `organization_id` (UUID), `middleware_id` (UUID) | None | `id`, `name`, `status` | Requires valid middleware reference |
| `provably_check_database_access` | Check database access credentials | `organization_id` (UUID), `middleware_id` (UUID), `name` (str), `username` (str), `password` (str), `provider` (str), `uri` (str) | None | `is_admin`, `is_credentials_correct` | Credentials are validated against the database |
| `provably_create_database` | Create a new database | `organization_id` (UUID), `middleware_id` (UUID), `name` (str), `username` (str), `password` (str), `provider` (str), `uri` (str) | None | `id`, `name` | Requires valid middleware reference |
| `provably_get_database` | Get database details | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID) | None | `id`, `name`, `username`, `provider`, `uri`, `is_admin`, `status`, `middleware` | Requires valid database reference |
| `provably_update_database` | Update database configuration | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `name` (str), `username` (str), `password` (str), `provider` (str), `uri` (str) | None | `id`, `name` | Requires valid database reference |
| `provably_bulk_delete_databases` | Bulk delete databases | `organization_id` (UUID), `database_ids` (list[UUID]) | None | `message` | Irreversible operation |

---

## Organizations

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_list_organizations` | List all organizations | None | None | `id`, `name`, `description`, `created_at`, `updated_at` | Returns organizations accessible to the authenticated user |
| `provably_add_organization` | Add a new organization | `name` (str) | `description` (str) | `id`, `name`, `description`, `created_at`, `updated_at` | User becomes admin of created organization |
| `provably_get_organization` | Get organization details | `organization_id` (UUID) | None | `id`, `name`, `description`, `created_at`, `updated_at` | Requires organization access |
| `provably_delete_organization` | Delete an organization | `organization_id` (UUID) | None | `message` | Requires admin access, irreversible |
| `provably_update_organization` | Update organization details | `organization_id` (str), `name` (str) | `description` (str) | `id`, `name`, `description`, `created_at`, `updated_at` | Requires admin access |
| `provably_leave_organization` | Leave an organization | `organization_id` (UUID) | None | `message` | Cannot leave if you're the only admin |
| `provably_get_users_in_organization` | Get users in an organization | `organization_id` (UUID) | `page`, `page_size`, `search` | `id`, `email`, `role`, `joined_at` | Requires organization access |
| `provably_update_organization_user` | Update user role in organization | `organization_id` (UUID), `user_id` (UUID), `role` (str) | None | `id`, `email`, `role`, `joined_at` | Requires admin access |
| `provably_bulk_invite_users_to_organization` | Bulk invite users to organization | `organization_id` (UUID), `emails` (list[str]) | `role` (str) | `message` | Requires admin access |
| `provably_bulk_delete_users_from_organization` | Bulk delete users from organization | `organization_id` (UUID), `user_ids` (list[UUID]) | None | `message` | Requires admin access |

---

## User Management

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_current_user` | Get current user information | None | None | `id`, `email`, `name`, `created_at`, `updated_at` | Returns authenticated user's data |
| `provably_update_current_user` | Update current user information | `name` (str) | `email` (str) | `id`, `email`, `name`, `created_at`, `updated_at` | Email changes may require verification |
| `provably_delete_current_user` | Delete current user account | `confirmation` (str) | None | `message` | Irreversible operation, requires confirmation |
| `provably_intiate_delete_current_user` | Initiate user account deletion | None | None | `message` | Starts deletion process |
| `provably_search_user` | Search for users | `query` (str) | `page`, `page_size` | `users` (list), `total`, `page`, `page_size` | Search results limited by privacy settings |
| `provably_get_user_api_key` | Get user API key | None | None | `api_key` (str) | Returns current user's API key |

---

## Middlewares

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_list_middlewares` | List all middlewares | `organization_id` (UUID) | None | `id`, `name`, `status`, `is_self_hosted` | Requires organization access |
| `provably_add_provably_middleware` | Add a new Provably middleware | `organization_id` (UUID) | None | `id`, `name`, `status`, `is_self_hosted` | Creates Provably-specific middleware |
| `provably_get_middleware` | Get middleware details | `organization_id` (UUID), `middleware_id` (UUID) | None | `id`, `name`, `status`, `is_self_hosted` | Requires middleware access |
| `provably_update_middleware` | Update middleware configuration | `organization_id` (UUID), `middleware_id` (UUID), `name` (str) | `status` (str) | `id`, `name`, `status`, `is_self_hosted` | Requires admin access |
| `provably_bulk_delete_middlewares` | Bulk delete middlewares | `organization_id` (UUID), `middleware_ids` (list[UUID]) | None | `message` | Irreversible operation |

---

## Contents

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_list_organization_contents` | List organization contents | `organization_id` (UUID) | `page`, `page_size`, `search`, `type` | `contents` (list), `total`, `page`, `page_size` | Requires organization access |
| `provably_search_organization_contents` | Search organization contents | `organization_id` (UUID), `query` (str) | None | `id`, `name`, `type`, `description`, `created_at` | Search results limited by content visibility |

---

## Validation

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_validate_database` | Validate a database | `organization_id` (UUID), `database_id` (UUID) | None | `message` | Validates database connectivity and schema |

---

## Query Records

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_query` | Get query details | `organization_id` (UUID), `query_id` (UUID) | None | `id`, `query`, `result`, `created_at`, `status` | Requires query access |
| `provably_download_proof` | Download a proof | `proof_id` (UUID) | None | `proof_data` (str) | Returns proof as string data |

---

## Data Hierarchy

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_data_hierarchy` | Get data hierarchy | `organization_id` (UUID) | None | `organizations`, `middlewares`, `databases`, `schemas`, `tables` | Returns complete data structure |

---

## Feedback

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_create_feedback` | Create feedback for Provably | `description` (str) | None | `message` | Feedback is sent to Provably team |

---

## Notifications

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_user_notifications` | Get user notifications | `page` (int), `page_size` (int) | `unread_only` (bool) | `notifications` (list), `total`, `page`, `page_size` | Pagination required |
| `provably_get_user_notifications_count` | Get user notifications count | None | None | `total`, `unread` | Returns count summary |
| `provably_delete_user_noficiation` | Delete user notification | `notification_id` (UUID) | None | `message` | Irreversible operation |
| `provably_get_notifications_settings` | Get notification settings | None | None | `settings` (list) | Returns user's notification preferences |
| `provably_update_notifications_settings` | Update notification settings | `settings` (list) | None | `settings` (list) | Updates user's notification preferences |

---

## Organization Logs

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_organization_logs` | Get organization logs | `organization_id` (UUID), `page` (int), `page_size` (int) | `start_date`, `end_date`, `level` | `logs` (list), `total`, `page`, `page_size` | Requires admin access |

---

## Organization Notifications

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_organization_notifications` | Get organization notifications | `organization_id` (UUID), `page` (int), `page_size` (int) | `unread_only` (bool) | `notifications` (list), `total`, `page`, `page_size` | Requires organization access |
| `provably_get_organization_notifications_settings` | Get organization notification settings | `organization_id` (UUID) | None | `settings` (list) | Requires admin access |
| `provably_update_organization_notifications_settings` | Update organization notification settings | `organization_id` (UUID), `settings` (list) | None | `settings` (list) | Requires admin access |
| `provably_get_organization_notifications_count` | Get organization notifications count | `organization_id` (UUID) | None | `count` (int) | Returns total notification count |

---

## Public

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_subscribe_to_newsletter` | Subscribe to Provably newsletter | `email` (str) | None | `message` | No authentication required |

---

## Schemas

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_bulk_create_database_schemas` | Bulk create database schemas | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schemas` (list) | None | `schemas` (list) | Requires valid database reference |
| `provably_get_database_schema` | Get database schema details | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID) | None | `id`, `name`, `tables`, `created_at` | Requires valid schema reference |
| `provably_update_database_schema` | Update database schema | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID), `name` (str) | `description` (str) | `id`, `name`, `description` | Requires admin access |
| `provably_get_database_schemas` | Get all database schemas | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID) | None | `schemas` (list) | Requires valid database reference |
| `provably_bulk_delete_database_schemas` | Bulk delete database schemas | `organization_id` (UUID), `schema_ids` (list[UUID]) | None | `message` | Irreversible operation |

---

## Tables

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `provably_get_database_tables` | Get all database tables | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID) | None | `tables` (list) | Requires valid schema reference |
| `provably_get_database_table` | Get database table details | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID), `table_id` (UUID) | None | `id`, `name`, `columns`, `created_at` | Requires valid table reference |
| `provably_update_database_table` | Update database table | `organization_id` (UUID), `middleware_id` (UUID), `database_id` (UUID), `schema_id` (UUID), `table_id` (UUID), `name` (str) | `description` (str) | `id`, `name`, `description` | Requires admin access |
| `provably_bulk_delete_database_tables` | Bulk delete database tables | `organization_id` (UUID), `table_ids` (list[UUID]) | None | `message` | Irreversible operation |

---

## Utility

| Tool Name | Description | Required Parameters | Optional Parameters | Response Fields | Limitations |
|-----------|-------------|-------------------|-------------------|-----------------|-------------|
| `send_hello` | Send a hello message | `name` (str) | None | `message` (str) | Simple greeting tool for testing |

---

## Common Data Types

### UUID Parameters
All ID parameters are UUIDs (Universally Unique Identifiers) in string format.

### Pagination
- `page`: Page number (0-based, minimum 0)
- `page_size`: Number of items per page (minimum 0)

### Sort Options
- `sort_order`: "asc" or "desc"
- `sort_by`: Field to sort by (varies by endpoint)

### Publicity Status (Collections)
- `PUBLIC`: Publicly accessible
- `PRIVATE`: Private to organization
- `LISTED`: Listed but restricted access
- `DRAFT`: Draft status

---

## Error Handling

All tools use structured error handling with the following common error types:
- `PMCPPermissionError`: Invalid or missing API key
- `PMCPValidationError`: Invalid request parameters
- `PMCPNotFoundError`: Resource not found
- `PMCPConflictError`: Resource conflict (e.g., duplicate names)

---

## Rate Limiting

The PMCP server respects Provably API rate limits. Excessive requests may result in temporary throttling.

---

## Security Notes

1. All API keys are validated on every request
2. All operations are logged for audit purposes
3. Sensitive data (passwords, API keys) is not returned in responses
4. Bulk operations are logged with full parameter details
5. User permissions are enforced at the API level

---

*This documentation is automatically generated from the PMCP server codebase. For the most up-to-date information, refer to the source code.*
