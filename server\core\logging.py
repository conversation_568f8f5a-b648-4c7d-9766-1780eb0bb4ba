import json
import logging
import sys

import fastmcp
import sentry_sdk
from loguru import logger

from server.core.config import Config

logging.getLogger("FastMCP").setLevel(logging.CRITICAL)
fastmcp.settings.enable_rich_tracebacks = False


class LogConfig:
    LOG_LEVEL: str = Config.LOG_LEVEL
    JSON_LOGS: bool = Config.IS_PROD_LIKE


def serialize(record) -> str:
    log_json = {
        "timestamp": record["time"].isoformat(),  # Standard ISO 8601 timestamp
        "level": record["level"].name,
        "message": record["message"],
        "logger_name": record["name"],
        # Contextual information
        "module": record["module"],
        "function": record["function"],
        "line": record["line"],
        "file_name": record["file"].name,  # Just filename, not full path
        "environment": Config.ENVIRONMENT,
        "version": Config.VERSION,
        "extra": record["extra"],
    }

    if record["exception"]:
        exception = record["exception"]
        log_json["exception"] = {
            "type": str(exception.type.__name__),
            "value": str(exception.value),
            "traceback": exception.traceback,
        }

    return json.dumps(log_json)


def setup_logging():
    logger.remove()

    # sentry_sdk.init(
    #     dsn=Config.SENTRY_DSN.get_secret_value() if Config.SENTRY_DSN else None,
    #     traces_sample_rate=0.2 if Config.IS_PROD_LIKE else 1.0,
    #     release=f"pmcp@{Config.VERSION}",
    #     environment=Config.ENVIRONMENT,
    # )

    # Configure sinks based on environment
    if LogConfig.JSON_LOGS:
        # Production: JSON for log aggregation to stdout
        logger.add(
            lambda msg: (
                print(serialize(msg.record), file=sys.stderr)
                if msg.record["level"].no >= logger.level("ERROR").no
                else print(serialize(msg.record), file=sys.stdout)
            ),
            enqueue=True,
            backtrace=False,
            diagnose=False,
            catch=True,
            level=LogConfig.LOG_LEVEL,
        )
    else:
        # Development: Colorized, user-specified format to stdout
        dev_format = (
            "<level>{time:YYYY-MM-DD HH:mm:ss.SSS} | {level.name:<8} | "
            "{name}:{function}:{line} - {message}</level>"
            "<cyan> | extra: {extra}</cyan>"
        )
        logger.add(
            sys.stdout,
            format=dev_format,
            diagnose=True,
            level=LogConfig.LOG_LEVEL,
        )
        logger.level("DEBUG", color="<blue>")
        logger.level("INFO", color="<green>")
        logger.level("WARNING", color="<yellow>")
        logger.level("ERROR", color="<red>")
        logger.level("CRITICAL", color="<magenta>")

    logger.info(
        "Logging initialized",
        environment=Config.ENVIRONMENT,
        version=Config.VERSION,
        log_level=LogConfig.LOG_LEVEL,
        json_logs=LogConfig.JSON_LOGS,
    )
    return logger
