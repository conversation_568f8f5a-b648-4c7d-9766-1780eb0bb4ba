from datetime import datetime
from typing import Literal, Union
from uuid import UUID
from pydantic import BaseModel, Field


class NotificationActivityCategory(BaseModel):
    in_app: bool
    email: bool


class OrganizationNotificationSettingsResponse(BaseModel):
    member_activity: NotificationActivityCategory
    resource_activity: NotificationActivityCategory


class UpdateOrganizationNotificationSettings(BaseModel):
    member_activity: NotificationActivityCategory | None = None
    resource_activity: NotificationActivityCategory | None = None


class OrganizationNotificationQueryParams(BaseModel):
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)


class OrganizationNotificationBase(BaseModel):
    id: UUID
    created_at: datetime
    is_read: bool


class NotificationUser(BaseModel):
    id: UUID
    username: str | None = None
    email: str | None = None
    avatar: str | None = None


class UserLeftOrganizationResponse(OrganizationNotificationBase):
    type: Literal["user_left_organization"]
    user: NotificationUser


class UserJoinedOrganizationResponse(OrganizationNotificationBase):
    type: Literal["user_joined_organization"]
    user: NotificationUser


class NotificationCollection(BaseModel):
    id: UUID
    name: str


class CollectionAccessRequestedResponse(OrganizationNotificationBase):
    type: Literal["collection_access_requested"]
    collection: NotificationCollection
    user: NotificationUser


class NotificationResource(BaseModel):
    type: str
    id: UUID
    name: str


class ResourceInvalidatedResponse(OrganizationNotificationBase):
    type: Literal["resource_invalidated"]
    resource: NotificationResource
    invalidated_collection_count: int


OrganizationNotificationResponse = list[
    Union[
        UserLeftOrganizationResponse,
        UserJoinedOrganizationResponse,
        CollectionAccessRequestedResponse,
        ResourceInvalidatedResponse,
    ]
]
